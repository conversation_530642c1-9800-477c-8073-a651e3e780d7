{"version": "6", "dialect": "sqlite", "id": "b5a0a200-a48e-4e67-8bb5-7eda076ddcc7", "prevId": "da70940c-38be-4644-acd1-7ed874c06752", "tables": {"notion": {"name": "notion", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "markdown": {"name": "markdown", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_edited": {"name": "last_edited", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "notion_embedding": {"name": "notion_embedding", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "article_id": {"name": "article_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "chunk_idx": {"name": "chunk_idx", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "embedding": {"name": "embedding", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content_hash": {"name": "content_hash", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"notion_embedding_article_id_notion_id_fk": {"name": "notion_embedding_article_id_notion_id_fk", "tableFrom": "notion_embedding", "tableTo": "notion", "columnsFrom": ["article_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}