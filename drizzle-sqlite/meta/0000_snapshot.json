{"version": "6", "dialect": "sqlite", "id": "da70940c-38be-4644-acd1-7ed874c06752", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"notion": {"name": "notion", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'[]'"}, "created_at": {"name": "created_at", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "markdown": {"name": "markdown", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "last_edited": {"name": "last_edited", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}