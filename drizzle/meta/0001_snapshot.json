{"id": "f0eb49dc-9f73-4034-9d98-6f6f0896688a", "prevId": "10d4b6d7-be7b-4d6f-a8c9-e41022b3a8c5", "version": "7", "dialect": "postgresql", "tables": {"public.article": {"name": "article", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'::text[]"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "markdown": {"name": "markdown", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": false}, "last_edited": {"name": "last_edited", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.embedding": {"name": "embedding", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "article_id": {"name": "article_id", "type": "text", "primaryKey": false, "notNull": true}, "chunk_idx": {"name": "chunk_idx", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": true}}, "indexes": {"embeddingIndex": {"name": "embeddingIndex", "columns": [{"expression": "embedding", "isExpression": false, "asc": true, "nulls": "last", "opclass": "vector_cosine_ops"}], "isUnique": false, "concurrently": false, "method": "hnsw", "with": {}}, "content_search_index": {"name": "content_search_index", "columns": [{"expression": "to_tsvector('english', \"content\")", "asc": true, "isExpression": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gin", "with": {}}}, "foreignKeys": {"embedding_article_id_article_id_fk": {"name": "embedding_article_id_article_id_fk", "tableFrom": "embedding", "tableTo": "article", "columnsFrom": ["article_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.status": {"name": "status", "schema": "public", "values": ["draft", "published", "archive", "in_review"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}