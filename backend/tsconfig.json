{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ES2020",
    "moduleResolution": "node",
    "outDir": "dist",
    "rootDir": "src",
    "baseUrl": "src",
    "paths": {
      "@schema/*": ["schema/*"],
      "@lib/*": ["lib/*"],
      "@agents": ["agents/index"],
    },
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true
  },
  "include": ["src"],
  "ts-node": {
    "esm": true,
    "experimentalSpecifierResolution": "node"
  }
} 